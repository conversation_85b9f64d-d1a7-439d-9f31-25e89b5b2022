# Docker Data Persistence Guide

## Problem
Docker containers lose data when restarted unless proper volume persistence is configured.

## Solution

### 1. Updated Docker Compose Configuration
The `docker-compose.yml` has been updated with:
- **Restart policies**: `restart: unless-stopped` for all services
- **Additional volumes**: Added volumes for RabbitMQ and PgAdmin
- **Better volume configuration**: Explicit driver specification

### 2. Proper Container Management Commands

#### ✅ SAFE Commands (Preserve Data)
```bash
# Stop containers (keeps volumes)
docker-compose stop

# Start containers (uses existing volumes)
docker-compose start

# Restart containers (preserves data)
docker-compose restart

# Update and restart (preserves data)
docker-compose up -d
```

#### ❌ DANGEROUS Commands (Can Lose Data)
```bash
# DON'T USE: Removes volumes and all data
docker-compose down -v

# DON'T USE: Removes everything including volumes
docker system prune -a --volumes
```

#### 🔄 Safe Update Process
```bash
# 1. Stop services
docker-compose stop

# 2. Pull latest images (if needed)
docker-compose pull

# 3. Start with new configuration
docker-compose up -d
```

### 3. Data Backup Commands

#### PostgreSQL Backup
```bash
# Create backup
docker exec postgres pg_dump -U postgres abstraxn > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore backup
docker exec -i postgres psql -U postgres abstraxn < backup_file.sql
```

#### Qdrant Backup
```bash
# Create backup directory
mkdir -p ./backups/qdrant

# Copy Qdrant data
docker cp qdrant:/qdrant/storage ./backups/qdrant/

# Restore Qdrant data
docker cp ./backups/qdrant/storage qdrant:/qdrant/
```

#### Full Volume Backup
```bash
# Backup all volumes
docker run --rm -v under_construction_postgres_data:/data -v $(pwd)/backups:/backup alpine tar czf /backup/postgres_backup_$(date +%Y%m%d).tar.gz -C /data .
docker run --rm -v under_construction_qdrant_storage:/data -v $(pwd)/backups:/backup alpine tar czf /backup/qdrant_backup_$(date +%Y%m%d).tar.gz -C /data .
```

### 4. Volume Management

#### Check Volume Status
```bash
# List all volumes
docker volume ls

# Inspect specific volume
docker volume inspect under_construction_postgres_data
docker volume inspect under_construction_qdrant_storage

# Check volume usage
docker system df -v
```

#### Manual Volume Creation (if needed)
```bash
# Create volumes manually
docker volume create under_construction_postgres_data
docker volume create under_construction_qdrant_storage
```

### 5. Alternative: Bind Mounts (More Control)

If you want even more control, you can use bind mounts by uncommenting the alternative lines in docker-compose.yml:

```yaml
volumes:
  # Use bind mounts instead of named volumes
  - ./data/postgres:/var/lib/postgresql/data
  - ./data/qdrant:/qdrant/storage
```

Then create the directories:
```bash
mkdir -p ./data/postgres
mkdir -p ./data/qdrant
```

### 6. Monitoring Data Persistence

#### Verify Data Persistence
```bash
# 1. Create test data in your application
# 2. Stop containers
docker-compose stop

# 3. Start containers
docker-compose start

# 4. Check if data still exists
```

### 7. Emergency Recovery

If you accidentally lose data:
```bash
# 1. Stop all containers
docker-compose stop

# 2. Check if volumes still exist
docker volume ls | grep under_construction

# 3. If volumes exist, restart
docker-compose start

# 4. If volumes are gone, restore from backup
# (Use backup commands above)
```

## Best Practices

1. **Always use `docker-compose stop/start`** instead of `down/up` for daily operations
2. **Regular backups**: Set up automated backups of your data
3. **Test restores**: Regularly test your backup/restore process
4. **Monitor volumes**: Keep an eye on volume disk usage
5. **Use restart policies**: Containers will auto-restart after system reboot

## Current Volume Status
Your current volumes:
- `under_construction_postgres_data` - PostgreSQL data
- `under_construction_qdrant_storage` - Qdrant vector database data

These should persist across container restarts when using the proper commands above.
