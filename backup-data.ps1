# PowerShell script to backup Docker data
# Usage: .\backup-data.ps1

$BackupDir = ".\backups"
$Date = Get-Date -Format "yyyyMMdd_HHmmss"

# Create backup directory if it doesn't exist
if (!(Test-Path $BackupDir)) {
    New-Item -ItemType Directory -Path $BackupDir
    Write-Host "Created backup directory: $BackupDir" -ForegroundColor Green
}

Write-Host "Starting backup process..." -ForegroundColor Yellow

# Backup PostgreSQL
Write-Host "Backing up PostgreSQL database..." -ForegroundColor Cyan
$PostgresBackup = "$BackupDir\postgres_backup_$Date.sql"
docker exec postgres pg_dump -U postgres abstraxn > $PostgresBackup
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ PostgreSQL backup completed: $PostgresBackup" -ForegroundColor Green
} else {
    Write-Host "❌ PostgreSQL backup failed" -ForegroundColor Red
}

# Backup Qdrant (volume backup)
Write-Host "Backing up Qdrant vector database..." -ForegroundColor Cyan
$QdrantBackup = "$BackupDir\qdrant_backup_$Date.tar.gz"
docker run --rm -v under_construction_qdrant_storage:/data -v ${PWD}/backups:/backup alpine tar czf /backup/qdrant_backup_$Date.tar.gz -C /data .
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Qdrant backup completed: $QdrantBackup" -ForegroundColor Green
} else {
    Write-Host "❌ Qdrant backup failed" -ForegroundColor Red
}

# Backup PostgreSQL volume (additional safety)
Write-Host "Backing up PostgreSQL volume..." -ForegroundColor Cyan
$PostgresVolumeBackup = "$BackupDir\postgres_volume_backup_$Date.tar.gz"
docker run --rm -v under_construction_postgres_data:/data -v ${PWD}/backups:/backup alpine tar czf /backup/postgres_volume_backup_$Date.tar.gz -C /data .
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ PostgreSQL volume backup completed: $PostgresVolumeBackup" -ForegroundColor Green
} else {
    Write-Host "❌ PostgreSQL volume backup failed" -ForegroundColor Red
}

Write-Host "`nBackup process completed!" -ForegroundColor Yellow
Write-Host "Backup files are stored in: $BackupDir" -ForegroundColor Cyan

# List backup files
Write-Host "`nBackup files:" -ForegroundColor Cyan
Get-ChildItem $BackupDir | Sort-Object LastWriteTime -Descending | Select-Object Name, Length, LastWriteTime | Format-Table -AutoSize
