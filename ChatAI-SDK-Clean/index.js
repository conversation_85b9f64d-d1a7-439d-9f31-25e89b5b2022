#!/usr/bin/env node

/**
 * ChatAI SDK Clean - Simplified version
 * 
 * This is a clean, simplified version of the ChatAI SDK that:
 * - Has only ONE endpoint: /api/v1/?apikey=...&query=...
 * - Makes only ONE API call to User-Service: /users/app/key-validator
 * - Gets validation + documents in a single optimized call
 * - Maintains the same LlamaIndex and OpenRouter functionality
 */

require('dotenv').config();
require('./src/server');
