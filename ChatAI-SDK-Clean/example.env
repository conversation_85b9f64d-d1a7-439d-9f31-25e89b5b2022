# ChatAI SDK Clean Environment Configuration

# Server Configuration
PORT=3001
NODE_ENV=development

# PostgreSQL Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=abstraxn

# User Service Configuration (for integration)
USER_SERVICE_URL=http://localhost:3000

# ChatAI Origin for key validation
CHATAI_ORIGIN=http://localhost:3001

# Qdrant Vector Database Configuration
QDRANT_URL=http://localhost:6333
QDRANT_COLLECTION=chatai_documents

# Required API Keys
LLAMA_CLOUD_API_KEY=llx-your-llama-cloud-api-key-here
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-api-key-here

# Required API Key for embeddings
OPENAI_API_KEY=sk-your-openai-api-key-here

# Cache Configuration
CACHE_TTL_MINUTES=15
MAX_SESSIONS=1000
CLEANUP_INTERVAL_MINUTES=5

# Rate Limiting
RATE_LIMIT_WINDOW_MINUTES=15
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# Internal API Configuration (for service communication)
INTERNAL_API_KEY=chatai-internal-2024

# Embedding Cache Configuration
EMBEDDING_CACHE_ENABLED=true
EMBEDDING_CACHE_MAX_SIZE=10000
EMBEDDING_CACHE_MAX_AGE=604800000
