#!/usr/bin/env node

/**
 * Test script for ChatAI SDK Clean
 * Tests the simplified single-endpoint flow
 */

const fetch = require('node-fetch');

// Configuration
const CHATAI_SDK_BASE_URL = process.env.CHATAI_SDK_BASE_URL || 'http://localhost:3002';
const TEST_API_KEY = process.env.TEST_API_KEY || 'test_api_key_1751884336144_vp9gospvg';

async function testMainEndpoint() {
  console.log('🧪 Testing ChatAI SDK Clean - Main Endpoint');
  console.log('=============================================\n');

  try {
    const testQuery = 'What is the main topic of the documents?';
    
    console.log(`🌐 Making request to: ${CHATAI_SDK_BASE_URL}/api/v1/`);
    console.log(`🗝️  API Key: ${TEST_API_KEY.substring(0, 20)}...`);
    console.log(`📝 Query: ${testQuery}`);
    console.log(`⏱️  Started at: ${new Date().toISOString()}\n`);
    
    const startTime = Date.now();
    
    const response = await fetch(
      `${CHATAI_SDK_BASE_URL}/api/v1/?apikey=${TEST_API_KEY}&query=${encodeURIComponent(testQuery)}&stream=false`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    const duration = Date.now() - startTime;

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Request failed: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    
    if (result.error) {
      throw new Error(`ChatAI-SDK error: ${result.message}`);
    }

    console.log(`✅ Request successful in ${duration}ms`);
    console.log(`📊 Documents used: ${result.documentsUsed}`);
    console.log(`📏 Context length: ${result.contextLength}`);
    console.log(`🆔 Session ID: ${result.sessionId}`);
    console.log(`⏱️  Timing breakdown:`, result.timing);
    console.log(`💾 Cache status:`, result.cached);
    console.log(`📝 Response preview: ${result.response.substring(0, 100)}...`);

    return true;

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

async function testHealthCheck() {
  console.log('\n🧪 Testing Health Check');
  console.log('========================\n');

  try {
    console.log(`🌐 Making request to: ${CHATAI_SDK_BASE_URL}/health`);
    
    const response = await fetch(`${CHATAI_SDK_BASE_URL}/health`);

    if (!response.ok) {
      throw new Error(`Health check failed: ${response.status}`);
    }

    const result = await response.json();
    
    console.log(`✅ Health check passed`);
    console.log(`📊 Status: ${result.status}`);
    console.log(`🕐 Timestamp: ${result.timestamp}`);
    console.log(`🔧 Service: ${result.service}`);
    console.log(`📋 Endpoints:`, result.endpoints);

    return true;

  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return false;
  }
}

async function testStreamingEndpoint() {
  console.log('\n🧪 Testing Streaming Endpoint');
  console.log('==============================\n');

  try {
    const testQuery = 'Tell me about the invoice amount';
    
    console.log(`🌐 Making streaming request to: ${CHATAI_SDK_BASE_URL}/api/v1/`);
    console.log(`📝 Query: ${testQuery}`);
    console.log(`🔄 Stream: true\n`);
    
    const response = await fetch(
      `${CHATAI_SDK_BASE_URL}/api/v1/?apikey=${TEST_API_KEY}&query=${encodeURIComponent(testQuery)}&stream=true`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`Streaming request failed: ${response.status}`);
    }

    console.log(`✅ Streaming response started`);
    console.log(`📡 Content-Type: ${response.headers.get('content-type')}`);
    
    // Note: In a real test, you would parse the SSE stream
    // For simplicity, we're just checking that the response started
    
    return true;

  } catch (error) {
    console.error('❌ Streaming test failed:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting ChatAI SDK Clean Tests\n');
  
  const test1Success = await testMainEndpoint();
  const test2Success = await testHealthCheck();
  const test3Success = await testStreamingEndpoint();
  
  console.log('\n📋 Test Summary');
  console.log('================');
  console.log(`Main endpoint: ${test1Success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Health check: ${test2Success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Streaming: ${test3Success ? '✅ PASS' : '❌ FAIL'}`);
  
  if (test1Success && test2Success && test3Success) {
    console.log('\n🎉 All tests passed! ChatAI SDK Clean is working correctly.');
    console.log('⚡ The optimized single-endpoint flow is functional.');
  } else {
    console.log('\n❌ Some tests failed. Please check the implementation.');
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testMainEndpoint,
  testHealthCheck,
  testStreamingEndpoint
};
