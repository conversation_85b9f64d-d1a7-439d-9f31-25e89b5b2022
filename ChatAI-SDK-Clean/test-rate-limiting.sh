#!/bin/bash

# Test script for ChatAI Rate Limiting
# Tests the 2 requests per minute limit for chat endpoints

BASE_URL="http://localhost:3001/api/v1/"
API_KEY="test_api_key_1751884336144_vp9gospvg"

echo "🧪 Testing ChatAI Rate Limiting (2 requests per minute)"
echo "======================================================="
echo ""

# Function to make a request and show result
make_request() {
    local query="$1"
    local request_num="$2"
    
    echo "📤 Request $request_num: query='$query'"
    echo "⏰ Time: $(date '+%H:%M:%S')"
    
    response=$(curl -s "${BASE_URL}?apikey=${API_KEY}&query=${query}&stream=false")
    
    # Check if it's an error response
    if echo "$response" | jq -e '.error == true' >/dev/null 2>&1; then
        error_msg=$(echo "$response" | jq -r '.message')
        retry_after=$(echo "$response" | jq -r '.retryAfter // "N/A"')
        echo "❌ RATE LIMITED: $error_msg"
        echo "⏳ Retry after: $retry_after seconds"
    else
        echo "✅ SUCCESS: Request processed"
        # Show first 100 chars of response
        response_preview=$(echo "$response" | jq -r '.response // "No response"' | head -c 100)
        echo "📝 Response preview: $response_preview..."
    fi
    echo ""
}

# Test sequence
echo "🚀 Starting rate limit test..."
echo ""

# First request - should succeed
make_request "What is the current time?" 1

# Second request - should succeed
make_request "Tell me a joke" 2

# Third request - should be rate limited
make_request "What is 2+2?" 3

# Fourth request - should also be rate limited
make_request "Hello world" 4

echo "📊 Test Summary:"
echo "- First 2 requests should succeed"
echo "- Requests 3+ should be rate limited with 429 status"
echo "- Rate limit: 2 requests per 1 minute window"
echo "- Retry after: 60 seconds"
echo ""
echo "🔍 Check server logs for rate limiting messages:"
echo "   🚫 Chat rate limit exceeded for ::1"
