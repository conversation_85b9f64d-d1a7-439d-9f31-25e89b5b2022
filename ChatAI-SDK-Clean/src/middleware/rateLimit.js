const rateLimit = require('express-rate-limit');
const config = require('../config');

// Create rate limiter
const createRateLimit = rateLimit({
  windowMs: config.rateLimit.windowMinutes * 60 * 1000, // Convert to milliseconds
  max: config.rateLimit.maxRequests, // Limit each IP to max requests per windowMs
  message: {
    error: true,
    message: `Too many requests from this IP, please try again after ${config.rateLimit.windowMinutes} minutes.`,
    retryAfter: config.rateLimit.windowMinutes * 60 // seconds
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers

  // Custom key generator (can be enhanced to use user ID instead of IP)
  keyGenerator: (req) => {
    // Try to use user ID from auth token if available
    const authToken = req.headers.authorization;
    if (authToken) {
      try {
        // In a real implementation, you'd decode the JWT to get user ID
        // For now, we'll use IP + a hash of the token
        const tokenHash = require('crypto')
          .createHash('md5')
          .update(authToken)
          .digest('hex')
          .substring(0, 8);
        return `${req.ip}_${tokenHash}`;
      } catch (error) {
        // Fallback to IP
        return req.ip;
      }
    }
    return req.ip;
  },

  // Custom handler for rate limit exceeded
  handler: (req, res) => {
    console.warn(`🚫 Rate limit exceeded for ${req.ip} on ${req.path}`);

    res.status(429).json({
      error: true,
      message: `Too many requests. Please try again after ${config.rateLimit.windowMinutes} minutes.`,
      retryAfter: config.rateLimit.windowMinutes * 60,
      autoRetry: false,
      timestamp: new Date().toISOString()
    });
  },

  // Skip rate limiting for certain conditions
  skip: (req) => {
    // Skip rate limiting for health checks
    if (req.path === '/health') {
      return true;
    }

    // Skip for local development (both IPv4 and IPv6 localhost)
    if (config.nodeEnv === 'development' && (req.ip === '127.0.0.1' || req.ip === '::1')) {
      return true;
    }

    return false;
  }
});

// Stricter rate limit for chat endpoints
const chatRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 10, // Limit to 2 chat requests per minute
  message: {
    error: true,
    message: 'Too many chat requests. Please wait before sending another message.',
    retryAfter: 60
  },
  keyGenerator: (req) => {
    const authToken = req.headers.authorization;
    if (authToken) {
      try {
        const tokenHash = require('crypto')
          .createHash('md5')
          .update(authToken)
          .digest('hex')
          .substring(0, 8);
        return `chat_${req.ip}_${tokenHash}`;
      } catch (error) {
        return `chat_${req.ip}`;
      }
    }
    return `chat_${req.ip}`;
  },
  handler: (req, res) => {
    console.warn(`🚫 Chat rate limit exceeded for ${req.ip}`);

    res.status(429).json({
      error: true,
      message: 'You\'re sending messages too quickly. Please wait a moment before trying again.',
      userFriendlyMessage: 'Please wait a moment before sending another message. We\'ll automatically retry in 60 seconds.',
      retryAfter: 60,
      autoRetry: true,
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = createRateLimit;
module.exports.chatRateLimit = chatRateLimit;
