name: prod-backend-user-service-ci-deployment

on:
  push:
    branches:
      - prod
  pull_request:
    branches:
      - prod
  workflow_dispatch:

jobs:
  build:
    name: Build Image and Deploy to Kubernetes
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/prod'

    env:
      ECR_REPOSITORY: prod-backend-user-service
      IMAGE_TAG: ${{ github.run_number }}
      REGION: ap-south-1

    steps:
      - name: Check out code
        uses: actions/checkout@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PROD_AWS_SECRET_KEY }}
          aws-region: ap-south-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Set ECR registry environment variable
        run: echo "ECR_REGISTRY=${{ steps.login-ecr.outputs.registry }}" >> $GITHUB_ENV

      - name: Generate date-based tag
        id: date-tag
        run: |
          CURRENT_DATE=$(date +'%d-%m-%Y')
          CURRENT_TIME=$(date +'%H-%M-%S')
          IMAGE_TAG="${CURRENT_DATE}_${CURRENT_TIME}"
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV
          echo "Using tag: $IMAGE_TAG"

      - name: Build and tag Docker Image
        run: |
          docker build --no-cache -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .

      - name: Run Trivy file system scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'table'
          exit-code: 0

      - name: Push image to Amazon ECR
        run: |
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: Trigger Workflow B
        uses: peter-evans/repository-dispatch@v1
        with:
          token: ${{ secrets.PROD_GITHUB_TOKEN }}
          repository: Abstraxn-Labs/argo-cd
          event-type: trigger-workflow-prod-backend-user
          client-payload: |
            {
              "image_tag": "${{ env.IMAGE_TAG }}",
              "branch": "prod"
            }
