# Production Deployment Checklist

## ⚠️ CRITICAL: Development Bypasses to Remove Before Production

This file tracks all temporary bypasses and modifications made for development/testing that **MUST** be reverted before production deployment.

---

## 🔒 Authentication & Authorization Bypasses

### File: `src/application/application.service.ts`

#### 1. Plan Validation Bypass
**Location**: Lines 65-72 (approximately)
**Status**: ⚠️ BYPASSED FOR DEVELOPMENT
**Action Required**: UNCOMMENT and RESTORE

```typescript
// CURRENTLY COMMENTED OUT - MUST RESTORE FOR PRODUCTION:
// if (!user.plan || !Plans[user.plan]) {
//   return {
//     error: true,
//     statusCode: HttpStatus.BAD_REQUEST,
//     message: AppMessage.PlanNotFound(),
//   };
// }
```

**Production Fix Required**:
- Ensure all users have valid plans assigned
- Verify Plans enum/object is properly configured
- Test plan validation logic thoroughly

---

#### 2. App Limit Validation Bypass
**Location**: Lines 85-91 (approximately)
**Status**: ⚠️ BYPASSED FOR DEVELOPMENT
**Action Required**: UNCOMMENT and RESTORE

```typescript
// CURRENTLY COMMENTED OUT - MUST RESTORE FOR PRODUCTION:
// if (appCount >= Plans[user.plan].appsLimit) {
//   return {
//     error: true,
//     statusCode: HttpStatus.BAD_REQUEST,
//     message: AppMessage.AppLimitExceed(Plans[user.plan].appsLimit),
//   };
// }
```

**Production Fix Required**:
- Verify Plans[].appsLimit configuration
- Test app limit enforcement
- Ensure proper error messages

---

#### 3. Gateway Service Bypass
**Location**: Lines 107-123 (approximately)
**Status**: ⚠️ BYPASSED FOR DEVELOPMENT
**Action Required**: REMOVE MOCK CODE and RESTORE REAL SERVICE

```typescript
// CURRENTLY USING MOCK API KEY - MUST RESTORE REAL SERVICE FOR PRODUCTION:
// const apiKey = await this.gatewayService.assignConsumerkey({
//   username: user.username,
// });
// if (apiKey.error) {
//   return apiKey;
// }

// REMOVE THIS MOCK CODE:
const mockApiKey = `test_api_key_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
```

**Production Fix Required**:
- Ensure gateway service is accessible and configured
- Test API key generation and assignment
- Implement proper error handling for service failures
- Configure proper timeout and retry logic

---

## 🔍 Pre-Production Testing Checklist

### Authentication Flow
- [ ] User registration with plan assignment
- [ ] Plan validation during app creation
- [ ] App limit enforcement per plan type
- [ ] API key generation and assignment

### External Services
- [ ] Gateway service connectivity
- [ ] API key service functionality
- [ ] Proper error handling for service failures
- [ ] Timeout and retry configurations

### Database Operations
- [ ] Application creation and persistence
- [ ] User-app relationships
- [ ] Plan-based restrictions

---

## 🚀 Production Deployment Steps

### 1. Code Restoration
1. **Uncomment plan validation** in `application.service.ts`
2. **Uncomment app limit validation** in `application.service.ts`
3. **Remove mock API key generation** in `application.service.ts`
4. **Restore real gateway service calls** in `application.service.ts`

### 2. Environment Configuration
- [ ] Verify gateway service endpoints
- [ ] Configure API timeouts
- [ ] Set up proper error logging
- [ ] Configure plan definitions

### 3. Testing
- [ ] Run full test suite
- [ ] Test with real gateway service
- [ ] Verify plan-based restrictions
- [ ] Test error scenarios

### 4. Monitoring
- [ ] Set up alerts for gateway service failures
- [ ] Monitor API key generation success rates
- [ ] Track plan validation errors

---

## 📝 Notes

**Created**: 2025-07-03
**Last Updated**: 2025-07-03
**Created By**: Development Team
**Purpose**: Track temporary development bypasses for ChatAI application creation

**⚠️ WARNING**: Deploying to production without addressing these bypasses will result in:
- Security vulnerabilities (no plan validation)
- Unlimited app creation (no limits enforced)
- Non-functional API keys (mock keys instead of real ones)

---

## 🔄 Verification Commands

After restoring production code, verify with these commands:

```bash
# Test plan validation
curl -X POST "http://localhost:3000/users/app/create-app" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"appName": "Test", "appDescription": "Test", "chainId": 1}'

# Should return plan validation error for users without valid plans

# Test app limits
# Create multiple apps to verify limit enforcement

# Test gateway service
# Verify real API keys are generated
```

---

**🚨 REMINDER**: This file should be reviewed and all items addressed before any production deployment!
